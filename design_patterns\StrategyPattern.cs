/*
Strategy Pattern - نمط الاستراتيجية
==================================

المفهوم: تعريف عائلة من الخوارزميات وجعلها قابلة للتبديل في وقت التشغيل
الهدف: فصل الخوارزميات عن الكود الذي يستخدمها

مثال عملي: نظام الدفع الإلكتروني
*/

using System;
using System.Collections.Generic;
using System.Linq;

namespace DesignPatterns.Strategy
{
    // 1. واجهة الاستراتيجية الأساسية
    public interface IPaymentStrategy
    {
        /// <summary>
        /// تنفيذ عملية الدفع
        /// </summary>
        PaymentResult ProcessPayment(decimal amount, PaymentDetails details);

        /// <summary>
        /// التحقق من صحة بيانات الدفع
        /// </summary>
        bool ValidatePaymentDetails(PaymentDetails details);

        /// <summary>
        /// الحصول على اسم طريقة الدفع
        /// </summary>
        string GetPaymentMethodName();
    }

    // 2. كلاس بيانات الدفع
    public class PaymentDetails
    {
        public string CardNumber { get; set; } = "";
        public string CardHolderName { get; set; } = "";
        public string ExpiryDate { get; set; } = "";
        public string CVV { get; set; } = "";
        public string Email { get; set; } = "";
        public string PhoneNumber { get; set; } = "";
        public Dictionary<string, object> AdditionalData { get; set; } = new();
    }

    // 3. كلاس نتيجة الدفع
    public class PaymentResult
    {
        public bool IsSuccess { get; set; }
        public string TransactionId { get; set; } = "";
        public string Message { get; set; } = "";
        public decimal ProcessedAmount { get; set; }
        public DateTime ProcessedAt { get; set; }
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();
    }

    // 4. تطبيق استراتيجيات الدفع المختلفة

    // استراتيجية الدفع بالبطاقة الائتمانية
    public class CreditCardPaymentStrategy : IPaymentStrategy
    {
        public string GetPaymentMethodName() => "Credit Card";

        public bool ValidatePaymentDetails(PaymentDetails details)
        {
            // التحقق من صحة بيانات البطاقة الائتمانية
            if (string.IsNullOrEmpty(details.CardNumber) || details.CardNumber.Length < 16)
                return false;

            if (string.IsNullOrEmpty(details.CardHolderName))
                return false;

            if (string.IsNullOrEmpty(details.ExpiryDate) || details.ExpiryDate.Length != 5)
                return false;

            if (string.IsNullOrEmpty(details.CVV) || details.CVV.Length < 3)
                return false;

            return true;
        }

        public PaymentResult ProcessPayment(decimal amount, PaymentDetails details)
        {
            Console.WriteLine($"معالجة دفع بالبطاقة الائتمانية: {amount:C}");

            if (!ValidatePaymentDetails(details))
            {
                return new PaymentResult
                {
                    IsSuccess = false,
                    Message = "بيانات البطاقة الائتمانية غير صحيحة",
                    ProcessedAt = DateTime.Now
                };
            }

            // محاكاة معالجة الدفع
            System.Threading.Thread.Sleep(1000); // محاكاة وقت المعالجة

            // محاكاة نجاح/فشل العملية (90% نجاح)
            var random = new Random();
            bool isSuccess = random.Next(1, 11) <= 9;

            return new PaymentResult
            {
                IsSuccess = isSuccess,
                TransactionId = $"CC_{Guid.NewGuid().ToString()[..8]}",
                Message = isSuccess ? "تم الدفع بنجاح" : "فشل في معالجة الدفع",
                ProcessedAmount = isSuccess ? amount : 0,
                ProcessedAt = DateTime.Now,
                AdditionalInfo = new Dictionary<string, object>
                {
                    { "card_last_four", details.CardNumber[^4..] },
                    { "payment_method", "credit_card" }
                }
            };
        }
    }

    // استراتيجية الدفع عبر PayPal
    public class PayPalPaymentStrategy : IPaymentStrategy
    {
        public string GetPaymentMethodName() => "PayPal";

        public bool ValidatePaymentDetails(PaymentDetails details)
        {
            // التحقق من صحة بيانات PayPal
            if (string.IsNullOrEmpty(details.Email) || !details.Email.Contains("@"))
                return false;

            return true;
        }

        public PaymentResult ProcessPayment(decimal amount, PaymentDetails details)
        {
            Console.WriteLine($"معالجة دفع عبر PayPal: {amount:C}");

            if (!ValidatePaymentDetails(details))
            {
                return new PaymentResult
                {
                    IsSuccess = false,
                    Message = "بيانات PayPal غير صحيحة",
                    ProcessedAt = DateTime.Now
                };
            }

            // محاكاة معالجة الدفع
            System.Threading.Thread.Sleep(800);

            // محاكاة نجاح/فشل العملية (95% نجاح)
            var random = new Random();
            bool isSuccess = random.Next(1, 21) <= 19;

            return new PaymentResult
            {
                IsSuccess = isSuccess,
                TransactionId = $"PP_{Guid.NewGuid().ToString()[..8]}",
                Message = isSuccess ? "تم الدفع عبر PayPal بنجاح" : "فشل في الدفع عبر PayPal",
                ProcessedAmount = isSuccess ? amount : 0,
                ProcessedAt = DateTime.Now,
                AdditionalInfo = new Dictionary<string, object>
                {
                    { "paypal_email", details.Email },
                    { "payment_method", "paypal" }
                }
            };
        }
    }

    // استراتيجية الدفع عبر التحويل البنكي
    public class BankTransferPaymentStrategy : IPaymentStrategy
    {
        public string GetPaymentMethodName() => "Bank Transfer";

        public bool ValidatePaymentDetails(PaymentDetails details)
        {
            // التحقق من صحة بيانات التحويل البنكي
            if (!details.AdditionalData.ContainsKey("bank_account") ||
                string.IsNullOrEmpty(details.AdditionalData["bank_account"].ToString()))
                return false;

            if (!details.AdditionalData.ContainsKey("routing_number") ||
                string.IsNullOrEmpty(details.AdditionalData["routing_number"].ToString()))
                return false;

            return true;
        }

        public PaymentResult ProcessPayment(decimal amount, PaymentDetails details)
        {
            Console.WriteLine($"معالجة دفع عبر التحويل البنكي: {amount:C}");

            if (!ValidatePaymentDetails(details))
            {
                return new PaymentResult
                {
                    IsSuccess = false,
                    Message = "بيانات التحويل البنكي غير صحيحة",
                    ProcessedAt = DateTime.Now
                };
            }

            // محاكاة معالجة الدفع (أبطأ من الطرق الأخرى)
            System.Threading.Thread.Sleep(2000);

            // محاكاة نجاح/فشل العملية (85% نجاح)
            var random = new Random();
            bool isSuccess = random.Next(1, 21) <= 17;

            return new PaymentResult
            {
                IsSuccess = isSuccess,
                TransactionId = $"BT_{Guid.NewGuid().ToString()[..8]}",
                Message = isSuccess ? "تم التحويل البنكي بنجاح" : "فشل في التحويل البنكي",
                ProcessedAmount = isSuccess ? amount : 0,
                ProcessedAt = DateTime.Now,
                AdditionalInfo = new Dictionary<string, object>
                {
                    { "bank_account", details.AdditionalData["bank_account"] },
                    { "payment_method", "bank_transfer" }
                }
            };
        }
    }

    // 5. Context - معالج الدفع الرئيسي
    public class PaymentProcessor
    {
        private IPaymentStrategy _paymentStrategy;
        private List<PaymentResult> _transactionHistory;

        public PaymentProcessor(IPaymentStrategy paymentStrategy)
        {
            _paymentStrategy = paymentStrategy;
            _transactionHistory = new List<PaymentResult>();
        }

        // تغيير استراتيجية الدفع في وقت التشغيل
        public void SetPaymentStrategy(IPaymentStrategy paymentStrategy)
        {
            _paymentStrategy = paymentStrategy;
            Console.WriteLine($"تم تغيير طريقة الدفع إلى: {paymentStrategy.GetPaymentMethodName()}");
        }

        public PaymentResult ProcessPayment(decimal amount, PaymentDetails details)
        {
            Console.WriteLine($"\n--- بدء معالجة الدفع باستخدام {_paymentStrategy.GetPaymentMethodName()} ---");

            var result = _paymentStrategy.ProcessPayment(amount, details);
            _transactionHistory.Add(result);

            Console.WriteLine($"نتيجة المعاملة: {result.Message}");
            if (result.IsSuccess)
            {
                Console.WriteLine($"رقم المعاملة: {result.TransactionId}");
            }

            return result;
        }

        public List<PaymentResult> GetTransactionHistory()
        {
            return new List<PaymentResult>(_transactionHistory);
        }

        public void PrintTransactionSummary()
        {
            Console.WriteLine("\n=== ملخص المعاملات ===");
            Console.WriteLine($"إجمالي المعاملات: {_transactionHistory.Count}");

            var successfulTransactions = _transactionHistory.Where(t => t.IsSuccess).ToList();
            var totalAmount = successfulTransactions.Sum(t => t.ProcessedAmount);

            Console.WriteLine($"المعاملات الناجحة: {successfulTransactions.Count}");
            Console.WriteLine($"إجمالي المبلغ المعالج: {totalAmount:C}");

            var failedTransactions = _transactionHistory.Count - successfulTransactions.Count;
            Console.WriteLine($"المعاملات الفاشلة: {failedTransactions}");
        }
    }

    // 6. مثال على الاستخدام
    public class StrategyPatternDemo
    {
        public static void DemonstrateStrategyPattern()
        {
            Console.WriteLine("=== Strategy Pattern Demo ===\n");

            // إنشاء بيانات دفع مختلفة
            var creditCardDetails = new PaymentDetails
            {
                CardNumber = "**********123456",
                CardHolderName = "أحمد محمد",
                ExpiryDate = "12/25",
                CVV = "123"
            };

            var paypalDetails = new PaymentDetails
            {
                Email = "<EMAIL>"
            };

            var bankTransferDetails = new PaymentDetails();
            bankTransferDetails.AdditionalData["bank_account"] = "**********";
            bankTransferDetails.AdditionalData["routing_number"] = "*********";

            // إنشاء معالج الدفع مع استراتيجية البطاقة الائتمانية
            var paymentProcessor = new PaymentProcessor(new CreditCardPaymentStrategy());

            // معالجة دفعات مختلفة
            paymentProcessor.ProcessPayment(100.50m, creditCardDetails);

            // تغيير الاستراتيجية إلى PayPal
            paymentProcessor.SetPaymentStrategy(new PayPalPaymentStrategy());
            paymentProcessor.ProcessPayment(75.25m, paypalDetails);

            // تغيير الاستراتيجية إلى التحويل البنكي
            paymentProcessor.SetPaymentStrategy(new BankTransferPaymentStrategy());
            paymentProcessor.ProcessPayment(200.00m, bankTransferDetails);

            // العودة إلى البطاقة الائتمانية
            paymentProcessor.SetPaymentStrategy(new CreditCardPaymentStrategy());
            paymentProcessor.ProcessPayment(50.75m, creditCardDetails);

            // طباعة ملخص المعاملات
            paymentProcessor.PrintTransactionSummary();

            // عرض تفاصيل المعاملات
            Console.WriteLine("\n=== تفاصيل المعاملات ===");
            var history = paymentProcessor.GetTransactionHistory();
            for (int i = 0; i < history.Count; i++)
            {
                var transaction = history[i];
                Console.WriteLine($"المعاملة {i + 1}:");
                Console.WriteLine($"  الحالة: {(transaction.IsSuccess ? "نجحت" : "فشلت")}");
                Console.WriteLine($"  المبلغ: {transaction.ProcessedAmount:C}");
                Console.WriteLine($"  رقم المعاملة: {transaction.TransactionId}");
                Console.WriteLine($"  الوقت: {transaction.ProcessedAt:yyyy-MM-dd HH:mm:ss}");
                Console.WriteLine();
            }
        }
    }
}