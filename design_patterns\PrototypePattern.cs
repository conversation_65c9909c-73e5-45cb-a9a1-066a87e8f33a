/*
Prototype Pattern - نمط النموذج الأولي
==================================

المفهوم: إنشاء كائنات جديدة عن طريق استنساخ كائن موجود
الهدف: تجنب التعقيد والتكلفة في إنشاء كائنات جديدة من الصفر

مثال عملي: نظام إدارة الوثائق
*/

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text.Json;

namespace DesignPatterns.Prototype
{
    // 1. الواجهة الأساسية للنموذج الأولي
    public interface IPrototype<T>
    {
        /// <summary>
        /// إنشاء نسخة من الكائن الحالي
        /// </summary>
        T Clone();
    }

    // 2. كلاس الوثيقة الأساسي
    public class Document : IPrototype<Document>
    {
        public string Title { get; set; }
        public string Content { get; set; }
        public Dictionary<string, object> Metadata { get; set; }
        public DateTime CreationTime { get; set; }

        public Document(string title, string content)
        {
            Title = title;
            Content = content;
            Metadata = new Dictionary<string, object>();
            CreationTime = DateTime.Now;
        }

        // Copy constructor للاستنساخ
        protected Document(Document original)
        {
            Title = original.Title;
            Content = original.Content;
            CreationTime = original.CreationTime;

            // نسخة عميقة للـ Metadata
            Metadata = new Dictionary<string, object>();
            foreach (var item in original.Metadata)
            {
                Metadata[item.Key] = item.Value;
            }
        }

        public virtual Document Clone()
        {
            return new Document(this);
        }

        public void SetTitle(string title)
        {
            Title = title;
        }

        public void SetContent(string content)
        {
            Content = content;
        }

        public void AddMetadata(string key, object value)
        {
            Metadata[key] = value;
        }

        public override string ToString()
        {
            var contentPreview = Content.Length > 50 ? Content.Substring(0, 50) + "..." : Content;
            var metadataJson = JsonSerializer.Serialize(Metadata);
            return $"Document: {Title}\nContent: {contentPreview}\nMetadata: {metadataJson}";
        }
    }

    // 3. أنواع مختلفة من الوثائق
    public class ReportDocument : Document
    {
        public string ReportType { get; set; }
        public List<Dictionary<string, object>> Charts { get; set; }

        public ReportDocument(string title, string content, string reportType = "monthly")
            : base(title, content)
        {
            ReportType = reportType;
            Charts = new List<Dictionary<string, object>>();
            AddMetadata("document_type", "report");
        }

        // Copy constructor
        protected ReportDocument(ReportDocument original) : base(original)
        {
            ReportType = original.ReportType;

            // نسخة عميقة للـ Charts
            Charts = new List<Dictionary<string, object>>();
            foreach (var chart in original.Charts)
            {
                var newChart = new Dictionary<string, object>();
                foreach (var item in chart)
                {
                    newChart[item.Key] = item.Value;
                }
                Charts.Add(newChart);
            }
        }

        public void AddChart(Dictionary<string, object> chartData)
        {
            Charts.Add(chartData);
        }

        public override Document Clone()
        {
            var cloned = new ReportDocument(this);
            // يمكن إضافة منطق خاص بالاستنساخ هنا
            cloned.AddMetadata("cloned_from", this.Title);
            return cloned;
        }
    }

    public class ContractDocument : Document
    {
        public string ContractType { get; set; }
        public List<Dictionary<string, string>> Parties { get; set; }
        public Dictionary<string, string> Terms { get; set; }

        public ContractDocument(string title, string content, string contractType = "service")
            : base(title, content)
        {
            ContractType = contractType;
            Parties = new List<Dictionary<string, string>>();
            Terms = new Dictionary<string, string>();
            AddMetadata("document_type", "contract");
        }

        // Copy constructor
        protected ContractDocument(ContractDocument original) : base(original)
        {
            ContractType = original.ContractType;

            // نسخة عميقة للـ Parties
            Parties = new List<Dictionary<string, string>>();
            foreach (var party in original.Parties)
            {
                var newParty = new Dictionary<string, string>();
                foreach (var item in party)
                {
                    newParty[item.Key] = item.Value;
                }
                Parties.Add(newParty);
            }

            // نسخة عميقة للـ Terms
            Terms = new Dictionary<string, string>();
            foreach (var term in original.Terms)
            {
                Terms[term.Key] = term.Value;
            }
        }

        public void AddParty(string partyName, string role)
        {
            Parties.Add(new Dictionary<string, string>
            {
                { "name", partyName },
                { "role", role }
            });
        }

        public void AddTerm(string termKey, string termValue)
        {
            Terms[termKey] = termValue;
        }

        public override Document Clone()
        {
            return new ContractDocument(this);
        }
    }

    // 4. مدير النماذج الأولية (Prototype Manager)
    public class DocumentPrototypeManager
    {
        private Dictionary<string, Document> _prototypes;

        public DocumentPrototypeManager()
        {
            _prototypes = new Dictionary<string, Document>();
        }

        public void RegisterPrototype(string name, Document prototype)
        {
            _prototypes[name] = prototype;
        }

        public void UnregisterPrototype(string name)
        {
            if (_prototypes.ContainsKey(name))
            {
                _prototypes.Remove(name);
            }
        }

        public Document ClonePrototype(string name)
        {
            if (!_prototypes.ContainsKey(name))
            {
                throw new ArgumentException($"النموذج الأولي '{name}' غير موجود");
            }
            return _prototypes[name].Clone();
        }

        public List<string> ListPrototypes()
        {
            return new List<string>(_prototypes.Keys);
        }
    }

    // 5. مثال على الاستخدام
    public class PrototypePatternDemo
    {

        public static void DemonstratePrototypePattern()
        {
            Console.WriteLine("=== Prototype Pattern Demo ===\n");

            // إنشاء مدير النماذج الأولية
            var prototypeManager = new DocumentPrototypeManager();

            // إنشاء نماذج أولية أساسية
            var basicReport = new ReportDocument(
                "تقرير شهري أساسي",
                "هذا نموذج أساسي لتقرير شهري يحتوي على البيانات الأساسية...",
                "monthly"
            );
            basicReport.AddChart(new Dictionary<string, object> { { "type", "bar" }, { "data", "sales_data" } });
            basicReport.AddMetadata("template_version", "1.0");

            var basicContract = new ContractDocument(
                "عقد خدمات أساسي",
                "هذا نموذج أساسي لعقد خدمات يحتوي على الشروط الأساسية...",
                "service"
            );
            basicContract.AddParty("الشركة", "مقدم الخدمة");
            basicContract.AddParty("العميل", "متلقي الخدمة");
            basicContract.AddTerm("مدة العقد", "12 شهر");

            // تسجيل النماذج الأولية
            prototypeManager.RegisterPrototype("monthly_report_template", basicReport);
            prototypeManager.RegisterPrototype("service_contract_template", basicContract);

            Console.WriteLine("النماذج الأولية المتاحة: " + string.Join(", ", prototypeManager.ListPrototypes()));
            Console.WriteLine();

            // استنساخ وتخصيص التقارير
            Console.WriteLine("--- استنساخ تقرير يناير ---");
            var januaryReport = prototypeManager.ClonePrototype("monthly_report_template");
            januaryReport.SetTitle("تقرير يناير 2024");
            januaryReport.AddMetadata("month", "January");
            januaryReport.AddMetadata("year", "2024");

            Console.WriteLine("--- استنساخ تقرير فبراير ---");
            var februaryReport = prototypeManager.ClonePrototype("monthly_report_template");
            februaryReport.SetTitle("تقرير فبراير 2024");
            februaryReport.AddMetadata("month", "February");
            februaryReport.AddMetadata("year", "2024");

            // استنساخ وتخصيص العقود
            Console.WriteLine("\n--- استنساخ عقد العميل الأول ---");
            var client1Contract = prototypeManager.ClonePrototype("service_contract_template");
            client1Contract.SetTitle("عقد خدمات - شركة ABC");
            client1Contract.AddMetadata("client_name", "شركة ABC");
            ((ContractDocument)client1Contract).AddTerm("قيمة العقد", "50,000 ريال");

            Console.WriteLine("\n--- استنساخ عقد العميل الثاني ---");
            var client2Contract = prototypeManager.ClonePrototype("service_contract_template");
            client2Contract.SetTitle("عقد خدمات - شركة XYZ");
            client2Contract.AddMetadata("client_name", "شركة XYZ");
            ((ContractDocument)client2Contract).AddTerm("قيمة العقد", "75,000 ريال");

            // عرض النتائج
            Console.WriteLine("\n=== النتائج ===");
            Console.WriteLine("تقرير يناير:");
            Console.WriteLine(januaryReport);
            Console.WriteLine("\n" + new string('=', 50) + "\n");

            Console.WriteLine("عقد العميل الأول:");
            Console.WriteLine(client1Contract);
            Console.WriteLine("\n" + new string('=', 50) + "\n");

            // مقارنة الأداء
            Console.WriteLine("=== مقارنة الأداء ===");
            var stopwatch = new Stopwatch();

            // قياس وقت الإنشاء من الصفر
            stopwatch.Start();
            for (int i = 0; i < 1000; i++)
            {
                var newReport = new ReportDocument($"تقرير {i}", "محتوى التقرير...");
                newReport.AddChart(new Dictionary<string, object> { { "type", "bar" }, { "data", "data" } });
                newReport.AddMetadata("template_version", "1.0");
            }
            stopwatch.Stop();
            var creationTime = stopwatch.ElapsedMilliseconds;

            // قياس وقت الاستنساخ
            stopwatch.Restart();
            for (int i = 0; i < 1000; i++)
            {
                var clonedReport = prototypeManager.ClonePrototype("monthly_report_template");
                clonedReport.SetTitle($"تقرير {i}");
            }
            stopwatch.Stop();
            var cloningTime = stopwatch.ElapsedMilliseconds;

            Console.WriteLine($"وقت الإنشاء من الصفر: {creationTime} مللي ثانية");
            Console.WriteLine($"وقت الاستنساخ: {cloningTime} مللي ثانية");
            if (cloningTime > 0)
            {
                Console.WriteLine($"الاستنساخ أسرع بـ {(double)creationTime / cloningTime:F2} مرة");
            }
        }
    }
}