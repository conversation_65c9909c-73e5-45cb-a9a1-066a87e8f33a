/*
Prototype Pattern - نمط النموذج الأولي
==================================

المفهوم: إنشاء كائنات جديدة عن طريق استنساخ كائن موجود
الهدف: تجنب التعقيد والتكلفة في إنشاء كائنات جديدة من الصفر

مثال عملي: نظام إدارة الوثائق
*/

using System;
using System.Collections.Generic;
using System.Diagnostics;

// 1. الواجهة الأساسية للنموذج الأولي
public interface IPrototype<T>
{
    /// <summary>
    /// إنشاء نسخة من الكائن الحالي
    /// </summary>
    T Clone();
}

# 2. كلاس الوثيقة الأساسي
class Document(Prototype):
    """كلاس الوثيقة الذي يمكن استنساخه"""

    def __init__(self, title: str, content: str, metadata: Dict[str, Any] = None):
        self.title = title
        self.content = content
        self.metadata = metadata or {}
        self.creation_time = "2024-01-01 10:00:00"  # محاكاة وقت الإنشاء

    def clone(self):
        """استنساخ الوثيقة - نسخة عميقة"""
        # استخدام deep copy لضمان استقلالية النسخة الجديدة
        return copy.deepcopy(self)

    def shallow_clone(self):
        """استنساخ سطحي - مشاركة المراجع"""
        return copy.copy(self)

    def set_title(self, title: str):
        self.title = title

    def set_content(self, content: str):
        self.content = content

    def add_metadata(self, key: str, value: Any):
        self.metadata[key] = value

    def __str__(self):
        return f"Document: {self.title}\nContent: {self.content[:50]}...\nMetadata: {self.metadata}"

# 3. أنواع مختلفة من الوثائق
class ReportDocument(Document):
    """وثيقة تقرير مع خصائص إضافية"""

    def __init__(self, title: str, content: str, report_type: str = "monthly"):
        super().__init__(title, content)
        self.report_type = report_type
        self.charts = []
        self.add_metadata("document_type", "report")

    def add_chart(self, chart_data: Dict):
        self.charts.append(chart_data)

    def clone(self):
        """استنساخ مخصص للتقرير"""
        cloned = copy.deepcopy(self)
        # يمكن إضافة منطق خاص بالاستنساخ هنا
        cloned.add_metadata("cloned_from", self.title)
        return cloned

class ContractDocument(Document):
    """وثيقة عقد مع خصائص قانونية"""

    def __init__(self, title: str, content: str, contract_type: str = "service"):
        super().__init__(title, content)
        self.contract_type = contract_type
        self.parties = []
        self.terms = {}
        self.add_metadata("document_type", "contract")

    def add_party(self, party_name: str, role: str):
        self.parties.append({"name": party_name, "role": role})

    def add_term(self, term_key: str, term_value: str):
        self.terms[term_key] = term_value

# 4. مدير النماذج الأولية (Prototype Manager)
class DocumentPrototypeManager:
    """مدير النماذج الأولية - يحفظ ويدير النماذج المختلفة"""

    def __init__(self):
        self._prototypes: Dict[str, Prototype] = {}

    def register_prototype(self, name: str, prototype: Prototype):
        """تسجيل نموذج أولي جديد"""
        self._prototypes[name] = prototype

    def unregister_prototype(self, name: str):
        """إلغاء تسجيل نموذج أولي"""
        if name in self._prototypes:
            del self._prototypes[name]

    def clone_prototype(self, name: str):
        """استنساخ نموذج أولي محدد"""
        if name not in self._prototypes:
            raise ValueError(f"النموذج الأولي '{name}' غير موجود")
        return self._prototypes[name].clone()

    def list_prototypes(self):
        """عرض قائمة بالنماذج الأولية المتاحة"""
        return list(self._prototypes.keys())

# 5. مثال على الاستخدام
def demonstrate_prototype_pattern():
    """عرض توضيحي لنمط النموذج الأولي"""

    print("=== Prototype Pattern Demo ===\n")

    # إنشاء مدير النماذج الأولية
    prototype_manager = DocumentPrototypeManager()

    # إنشاء نماذج أولية أساسية
    basic_report = ReportDocument(
        title="تقرير شهري أساسي",
        content="هذا نموذج أساسي لتقرير شهري يحتوي على البيانات الأساسية...",
        report_type="monthly"
    )
    basic_report.add_chart({"type": "bar", "data": "sales_data"})
    basic_report.add_metadata("template_version", "1.0")

    basic_contract = ContractDocument(
        title="عقد خدمات أساسي",
        content="هذا نموذج أساسي لعقد خدمات يحتوي على الشروط الأساسية...",
        contract_type="service"
    )
    basic_contract.add_party("الشركة", "مقدم الخدمة")
    basic_contract.add_party("العميل", "متلقي الخدمة")
    basic_contract.add_term("مدة العقد", "12 شهر")

    # تسجيل النماذج الأولية
    prototype_manager.register_prototype("monthly_report_template", basic_report)
    prototype_manager.register_prototype("service_contract_template", basic_contract)

    print("النماذج الأولية المتاحة:", prototype_manager.list_prototypes())
    print()

    # استنساخ وتخصيص التقارير
    print("--- استنساخ تقرير يناير ---")
    january_report = prototype_manager.clone_prototype("monthly_report_template")
    january_report.set_title("تقرير يناير 2024")
    january_report.add_metadata("month", "January")
    january_report.add_metadata("year", "2024")

    print("--- استنساخ تقرير فبراير ---")
    february_report = prototype_manager.clone_prototype("monthly_report_template")
    february_report.set_title("تقرير فبراير 2024")
    february_report.add_metadata("month", "February")
    february_report.add_metadata("year", "2024")

    # استنساخ وتخصيص العقود
    print("\n--- استنساخ عقد العميل الأول ---")
    client1_contract = prototype_manager.clone_prototype("service_contract_template")
    client1_contract.set_title("عقد خدمات - شركة ABC")
    client1_contract.add_metadata("client_name", "شركة ABC")
    client1_contract.add_term("قيمة العقد", "50,000 ريال")

    print("\n--- استنساخ عقد العميل الثاني ---")
    client2_contract = prototype_manager.clone_prototype("service_contract_template")
    client2_contract.set_title("عقد خدمات - شركة XYZ")
    client2_contract.add_metadata("client_name", "شركة XYZ")
    client2_contract.add_term("قيمة العقد", "75,000 ريال")

    # عرض النتائج
    print("\n=== النتائج ===")
    print("تقرير يناير:")
    print(january_report)
    print("\n" + "="*50 + "\n")

    print("عقد العميل الأول:")
    print(client1_contract)
    print("\n" + "="*50 + "\n")

    # مقارنة الأداء
    print("=== مقارنة الأداء ===")
    import time

    # قياس وقت الإنشاء من الصفر
    start_time = time.time()
    for i in range(1000):
        new_report = ReportDocument(f"تقرير {i}", "محتوى التقرير...")
        new_report.add_chart({"type": "bar", "data": "data"})
        new_report.add_metadata("template_version", "1.0")
    creation_time = time.time() - start_time

    # قياس وقت الاستنساخ
    start_time = time.time()
    for i in range(1000):
        cloned_report = prototype_manager.clone_prototype("monthly_report_template")
        cloned_report.set_title(f"تقرير {i}")
    cloning_time = time.time() - start_time

    print(f"وقت الإنشاء من الصفر: {creation_time:.4f} ثانية")
    print(f"وقت الاستنساخ: {cloning_time:.4f} ثانية")
    print(f"الاستنساخ أسرع بـ {creation_time/cloning_time:.2f} مرة")

if __name__ == "__main__":
    demonstrate_prototype_pattern()