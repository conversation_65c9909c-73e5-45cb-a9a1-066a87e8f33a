using DesignPatterns.Prototype;
using DesignPatterns.Strategy;
using DesignPatterns.State;
using DesignPatterns.Structural;

namespace DesignPatterns
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("مرحباً بك في عرض Design Patterns!");
            Console.WriteLine("=====================================\n");

            while (true)
            {
                Console.WriteLine("اختر النمط الذي تريد مشاهدة مثال عليه:");
                Console.WriteLine("1. Prototype Pattern (Creational)");
                Console.WriteLine("2. Strategy Pattern (Behavioral)");
                Console.WriteLine("3. State Pattern (Behavioral)");
                Console.WriteLine("4. Structural Patterns (Facade, Adapter, Decorator, Proxy)");
                Console.WriteLine("5. عرض جميع الأمثلة");
                Console.WriteLine("0. خروج");
                Console.Write("\nاختيارك: ");

                var choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        Console.Clear();
                        PrototypePatternDemo.DemonstratePrototypePattern();
                        break;
                    case "2":
                        Console.Clear();
                        StrategyPatternDemo.DemonstrateStrategyPattern();
                        break;
                    case "3":
                        Console.Clear();
                        StatePatternDemo.DemonstrateStatePattern();
                        break;
                    case "4":
                        Console.Clear();
                        StructuralPatternsDemo.DemonstrateStructuralPatterns();
                        break;
                    case "5":
                        Console.Clear();
                        RunAllDemos();
                        break;
                    case "0":
                        Console.WriteLine("شكراً لك!");
                        return;
                    default:
                        Console.WriteLine("اختيار غير صحيح، يرجى المحاولة مرة أخرى.");
                        break;
                }

                Console.WriteLine("\nاضغط أي مفتاح للمتابعة...");
                Console.ReadKey();
                Console.Clear();
            }
        }

        static void RunAllDemos()
        {
            Console.WriteLine("=== عرض جميع الأمثلة ===\n");

            Console.WriteLine("1. Prototype Pattern:");
            Console.WriteLine("====================");
            PrototypePatternDemo.DemonstratePrototypePattern();

            Console.WriteLine("\n\n2. Strategy Pattern:");
            Console.WriteLine("===================");
            StrategyPatternDemo.DemonstrateStrategyPattern();

            Console.WriteLine("\n\n3. State Pattern:");
            Console.WriteLine("================");
            StatePatternDemo.DemonstrateStatePattern();

            Console.WriteLine("\n\n4. Structural Patterns:");
            Console.WriteLine("======================");
            StructuralPatternsDemo.DemonstrateStructuralPatterns();
        }
    }
}