# 🚀 مرجع سريع - أنماط التصميم

## 📊 جدول مقارنة سريع

| النمط | النوع | الهدف | مثال واقعي | الكلمة المفتاحية |
|-------|------|--------|-------------|------------------|
| **Prototype** | Creational | استنساخ الكائنات | نسخ ولصق | Clone |
| **Strategy** | Behavioral | تبديل الخوارزميات | طرق الدفع | Algorithm |
| **State** | Behavioral | تغيير السلوك | آلة البيع | Behavior |
| **Facade** | Structural | تبسيط النظام | واجهة التطبيق | Simplify |
| **Adapter** | Structural | تحويل الواجهة | محول الكهرباء | Convert |
| **Decorator** | Structural | إضافة وظائف | إضافات القهوة | Enhance |
| **Proxy** | Structural | التحكم في الوصول | الوكيل العقاري | Control |

## 🎯 أسئلة سريعة للمراجعة

### Prototype Pattern
- **Q**: متى نستخدم Prototype؟
- **A**: عندما يكون إنشاء الكائن مكلفاً أو معقداً

### Strategy Pattern
- **Q**: ما الفرق بين Strategy و if/else؟
- **A**: Strategy يوفر مرونة في تغيير الخوارزمية في وقت التشغيل

### State Pattern
- **Q**: متى نستخدم State بدلاً من Strategy؟
- **A**: عندما يتغير سلوك الكائن حسب حالته الداخلية

### Facade Pattern
- **Q**: ما فائدة Facade؟
- **A**: تبسيط التفاعل مع نظام معقد

### Adapter Pattern
- **Q**: متى نحتاج Adapter؟
- **A**: عندما نريد استخدام كلاس بواجهة مختلفة

### Decorator Pattern
- **Q**: ما ميزة Decorator على الوراثة؟
- **A**: المرونة في إضافة/إزالة الوظائف في وقت التشغيل

### Proxy Pattern
- **Q**: ما أنواع Proxy؟
- **A**: Virtual (Lazy), Protection (Access Control), Remote, Cache

## 🔥 نصائح للحفظ السريع

### الأنماط الإنشائية (Creational)
```
🏭 PROTOTYPE = CLONE
"انسخ بدلاً من إنشاء جديد"
```

### الأنماط السلوكية (Behavioral)
```
🎭 STRATEGY = ALGORITHM FAMILY
"عائلة خوارزميات قابلة للتبديل"

🎭 STATE = BEHAVIOR CHANGE
"تغيير السلوك حسب الحالة"
```

### الأنماط الهيكلية (Structural)
```
🏗️ FACADE = SIMPLE INTERFACE
"واجهة مبسطة لنظام معقد"

🏗️ ADAPTER = INTERFACE CONVERTER
"محول الواجهات"

🏗️ DECORATOR = ADD FEATURES
"إضافة وظائف جديدة"

🏗️ PROXY = ACCESS CONTROL
"التحكم في الوصول"
```

## ⚡ كود سريع للمراجعة

### Prototype
```csharp
interface IPrototype { IPrototype Clone(); }
class Document : IPrototype {
    public IPrototype Clone() => new Document(this);
}
```

### Strategy
```csharp
interface IStrategy { void Execute(); }
class Context {
    IStrategy strategy;
    void SetStrategy(IStrategy s) => strategy = s;
}
```

### State
```csharp
interface IState { void Handle(Context c); }
class Context {
    IState state;
    void SetState(IState s) => state = s;
}
```

### Facade
```csharp
class Facade {
    SubSystem1 s1; SubSystem2 s2;
    void SimpleOperation() { s1.Op(); s2.Op(); }
}
```

### Adapter
```csharp
class Adapter : ITarget {
    Adaptee adaptee;
    void Request() => adaptee.SpecificRequest();
}
```

### Decorator
```csharp
abstract class Decorator : IComponent {
    IComponent component;
    virtual void Operation() => component.Operation();
}
```

### Proxy
```csharp
class Proxy : ISubject {
    RealSubject real;
    void Request() { CheckAccess(); real.Request(); }
}
```

## 🎯 بالتوفيق في الاختبار!