/*
Structural Patterns - الأنماط الهيكلية
=====================================

1. Facade Pattern - نمط الواجهة
2. Adapter Pattern - نمط المحول
3. Decorator Pattern - نمط المزخرف
4. Proxy Pattern - نمط الوكيل

مثال عملي: نظام إدارة المكتبة الإلكترونية
*/

using System;
using System.Collections.Generic;
using System.Linq;

namespace DesignPatterns.Structural
{
    // ===== FACADE PATTERN =====
    // نمط الواجهة - توفير واجهة مبسطة لنظام معقد

    // الأنظمة الفرعية المعقدة
    public class DatabaseService
    {
        public void Connect()
        {
            Console.WriteLine("🔌 الاتصال بقاعدة البيانات...");
            System.Threading.Thread.Sleep(500);
        }

        public void Disconnect()
        {
            Console.WriteLine("🔌 قطع الاتصال بقاعدة البيانات");
        }

        public List<string> SearchBooks(string query)
        {
            Console.WriteLine($"🔍 البحث في قاعدة البيانات عن: {query}");
            return new List<string> { $"كتاب 1 - {query}", $"كتاب 2 - {query}" };
        }

        public bool ReserveBook(string bookId)
        {
            Console.WriteLine($"📖 حجز الكتاب: {bookId}");
            return true;
        }
    }

    public class AuthenticationService
    {
        public bool Login(string username, string password)
        {
            Console.WriteLine($"🔐 تسجيل دخول المستخدم: {username}");
            return username == "admin" && password == "password";
        }

        public void Logout(string username)
        {
            Console.WriteLine($"🔐 تسجيل خروج المستخدم: {username}");
        }

        public bool CheckPermissions(string username, string action)
        {
            Console.WriteLine($"✅ فحص صلاحيات {username} للعملية: {action}");
            return true;
        }
    }

    public class NotificationService
    {
        public void SendEmail(string email, string message)
        {
            Console.WriteLine($"📧 إرسال إيميل إلى {email}: {message}");
        }

        public void SendSMS(string phone, string message)
        {
            Console.WriteLine($"📱 إرسال رسالة نصية إلى {phone}: {message}");
        }
    }

    public class LoggingService
    {
        public void LogActivity(string username, string activity)
        {
            Console.WriteLine($"📝 تسجيل النشاط - {username}: {activity}");
        }

        public void LogError(string error)
        {
            Console.WriteLine($"❌ تسجيل خطأ: {error}");
        }
    }

    // الواجهة المبسطة (Facade)
    public class LibraryFacade
    {
        private DatabaseService _database;
        private AuthenticationService _auth;
        private NotificationService _notification;
        private LoggingService _logging;
        private string _currentUser = "";

        public LibraryFacade()
        {
            _database = new DatabaseService();
            _auth = new AuthenticationService();
            _notification = new NotificationService();
            _logging = new LoggingService();
        }

        public bool LoginUser(string username, string password)
        {
            Console.WriteLine("\n=== تسجيل دخول المستخدم ===");

            if (_auth.Login(username, password))
            {
                _currentUser = username;
                _logging.LogActivity(username, "تسجيل دخول ناجح");
                return true;
            }

            _logging.LogError($"فشل تسجيل دخول للمستخدم: {username}");
            return false;
        }

        public List<string> SearchAndReserveBook(string query, string email, string phone)
        {
            Console.WriteLine($"\n=== البحث وحجز الكتاب: {query} ===");

            if (string.IsNullOrEmpty(_currentUser))
            {
                Console.WriteLine("❌ يرجى تسجيل الدخول أولاً");
                return new List<string>();
            }

            // الاتصال بقاعدة البيانات
            _database.Connect();

            // البحث عن الكتب
            var books = _database.SearchBooks(query);

            if (books.Any())
            {
                // حجز أول كتاب
                var firstBook = books.First();
                if (_database.ReserveBook(firstBook))
                {
                    // إرسال إشعارات
                    _notification.SendEmail(email, $"تم حجز الكتاب: {firstBook}");
                    _notification.SendSMS(phone, $"تم حجز {firstBook}");

                    // تسجيل النشاط
                    _logging.LogActivity(_currentUser, $"حجز كتاب: {firstBook}");
                }
            }

            _database.Disconnect();
            return books;
        }

        public void LogoutUser()
        {
            Console.WriteLine("\n=== تسجيل خروج المستخدم ===");

            if (!string.IsNullOrEmpty(_currentUser))
            {
                _logging.LogActivity(_currentUser, "تسجيل خروج");
                _auth.Logout(_currentUser);
                _currentUser = "";
            }
        }
    }

    // ===== ADAPTER PATTERN =====
    // نمط المحول - تحويل واجهة كلاس إلى واجهة أخرى متوقعة

    // النظام القديم (Legacy System)
    public class OldBookSystem
    {
        public string GetBookInfo(int bookId)
        {
            return $"معلومات الكتاب القديمة - ID: {bookId}";
        }

        public bool CheckAvailability(int bookId)
        {
            return bookId % 2 == 0; // محاكاة: الكتب ذات الأرقام الزوجية متوفرة
        }

        public void BorrowBook(int bookId, string borrowerName)
        {
            Console.WriteLine($"📚 استعارة كتاب قديم - ID: {bookId} للمستعير: {borrowerName}");
        }
    }

    // النظام الجديد (New Interface)
    public interface IModernBookService
    {
        BookInfo GetBookDetails(string isbn);
        bool IsBookAvailable(string isbn);
        void LendBook(string isbn, string memberEmail);
    }

    public class BookInfo
    {
        public string ISBN { get; set; } = "";
        public string Title { get; set; } = "";
        public string Author { get; set; } = "";
        public bool IsAvailable { get; set; }
    }

    // المحول (Adapter)
    public class BookSystemAdapter : IModernBookService
    {
        private OldBookSystem _oldSystem;

        public BookSystemAdapter(OldBookSystem oldSystem)
        {
            _oldSystem = oldSystem;
        }

        public BookInfo GetBookDetails(string isbn)
        {
            // تحويل ISBN إلى ID رقمي للنظام القديم
            int bookId = ConvertISBNToId(isbn);
            string oldInfo = _oldSystem.GetBookInfo(bookId);

            return new BookInfo
            {
                ISBN = isbn,
                Title = $"كتاب محول - {oldInfo}",
                Author = "مؤلف مجهول",
                IsAvailable = _oldSystem.CheckAvailability(bookId)
            };
        }

        public bool IsBookAvailable(string isbn)
        {
            int bookId = ConvertISBNToId(isbn);
            return _oldSystem.CheckAvailability(bookId);
        }

        public void LendBook(string isbn, string memberEmail)
        {
            int bookId = ConvertISBNToId(isbn);
            // تحويل الإيميل إلى اسم للنظام القديم
            string borrowerName = memberEmail.Split('@')[0];
            _oldSystem.BorrowBook(bookId, borrowerName);
        }

        private int ConvertISBNToId(string isbn)
        {
            // محاكاة تحويل ISBN إلى ID رقمي
            return Math.Abs(isbn.GetHashCode()) % 1000;
        }
    }

    // ===== DECORATOR PATTERN =====
    // نمط المزخرف - إضافة سلوكيات جديدة للكائنات دون تعديل هيكلها

    // الواجهة الأساسية
    public interface IBook
    {
        string GetDescription();
        decimal GetPrice();
        void Display();
    }

    // الكتاب الأساسي
    public class BasicBook : IBook
    {
        public string Title { get; set; }
        public string Author { get; set; }
        public decimal BasePrice { get; set; }

        public BasicBook(string title, string author, decimal basePrice)
        {
            Title = title;
            Author = author;
            BasePrice = basePrice;
        }

        public virtual string GetDescription()
        {
            return $"📖 {Title} - {Author}";
        }

        public virtual decimal GetPrice()
        {
            return BasePrice;
        }

        public virtual void Display()
        {
            Console.WriteLine($"{GetDescription()} - السعر: {GetPrice():C}");
        }
    }

    // المزخرف الأساسي
    public abstract class BookDecorator : IBook
    {
        protected IBook _book;

        public BookDecorator(IBook book)
        {
            _book = book;
        }

        public virtual string GetDescription()
        {
            return _book.GetDescription();
        }

        public virtual decimal GetPrice()
        {
            return _book.GetPrice();
        }

        public virtual void Display()
        {
            _book.Display();
        }
    }

    // مزخرفات محددة
    public class HardcoverDecorator : BookDecorator
    {
        public HardcoverDecorator(IBook book) : base(book) { }

        public override string GetDescription()
        {
            return _book.GetDescription() + " 📘 (غلاف صلب)";
        }

        public override decimal GetPrice()
        {
            return _book.GetPrice() + 15.00m; // إضافة تكلفة الغلاف الصلب
        }
    }

    public class GiftWrapDecorator : BookDecorator
    {
        public GiftWrapDecorator(IBook book) : base(book) { }

        public override string GetDescription()
        {
            return _book.GetDescription() + " 🎁 (تغليف هدية)";
        }

        public override decimal GetPrice()
        {
            return _book.GetPrice() + 5.00m; // إضافة تكلفة التغليف
        }
    }

    public class ExpressShippingDecorator : BookDecorator
    {
        public ExpressShippingDecorator(IBook book) : base(book) { }

        public override string GetDescription()
        {
            return _book.GetDescription() + " 🚚 (شحن سريع)";
        }

        public override decimal GetPrice()
        {
            return _book.GetPrice() + 10.00m; // إضافة تكلفة الشحن السريع
        }
    }

    public class DigitalVersionDecorator : BookDecorator
    {
        public DigitalVersionDecorator(IBook book) : base(book) { }

        public override string GetDescription()
        {
            return _book.GetDescription() + " 💻 (نسخة رقمية)";
        }

        public override decimal GetPrice()
        {
            return _book.GetPrice() * 0.7m; // خصم 30% للنسخة الرقمية
        }
    }

    // ===== PROXY PATTERN =====
    // نمط الوكيل - توفير بديل أو وكيل لكائن آخر للتحكم في الوصول إليه

    // الواجهة المشتركة
    public interface IBookDownloadService
    {
        void DownloadBook(string isbn, string userEmail);
        string GetBookContent(string isbn);
    }

    // الخدمة الحقيقية (مكلفة)
    public class RealBookDownloadService : IBookDownloadService
    {
        public void DownloadBook(string isbn, string userEmail)
        {
            Console.WriteLine($"🔄 بدء تحميل الكتاب {isbn} للمستخدم {userEmail}");

            // محاكاة عملية تحميل مكلفة
            Console.WriteLine("📥 جاري تحميل الكتاب من الخادم...");
            System.Threading.Thread.Sleep(2000); // محاكاة وقت التحميل

            Console.WriteLine($"✅ تم تحميل الكتاب {isbn} بنجاح");
        }

        public string GetBookContent(string isbn)
        {
            Console.WriteLine($"📖 جاري قراءة محتوى الكتاب {isbn}...");
            System.Threading.Thread.Sleep(1000); // محاكاة وقت القراءة

            return $"محتوى الكتاب {isbn} - هذا نص تجريبي للكتاب...";
        }
    }

    // الوكيل (Proxy)
    public class BookDownloadProxy : IBookDownloadService
    {
        private RealBookDownloadService? _realService;
        private Dictionary<string, string> _cache;
        private Dictionary<string, List<string>> _userDownloads;
        private const int MAX_DOWNLOADS_PER_USER = 3;

        public BookDownloadProxy()
        {
            _cache = new Dictionary<string, string>();
            _userDownloads = new Dictionary<string, List<string>>();
        }

        public void DownloadBook(string isbn, string userEmail)
        {
            // فحص الصلاحيات
            if (!CheckDownloadPermission(userEmail))
            {
                Console.WriteLine($"❌ تم تجاوز الحد الأقصى للتحميلات للمستخدم {userEmail}");
                return;
            }

            // تسجيل التحميل
            RecordDownload(userEmail, isbn);

            // إنشاء الخدمة الحقيقية عند الحاجة (Lazy Loading)
            if (_realService == null)
            {
                Console.WriteLine("🔧 إنشاء خدمة التحميل الحقيقية...");
                _realService = new RealBookDownloadService();
            }

            // استدعاء الخدمة الحقيقية
            _realService.DownloadBook(isbn, userEmail);
        }

        public string GetBookContent(string isbn)
        {
            // فحص الكاش أولاً
            if (_cache.ContainsKey(isbn))
            {
                Console.WriteLine($"📋 استرجاع محتوى الكتاب {isbn} من الكاش");
                return _cache[isbn];
            }

            // إنشاء الخدمة الحقيقية عند الحاجة
            if (_realService == null)
            {
                Console.WriteLine("🔧 إنشاء خدمة التحميل الحقيقية...");
                _realService = new RealBookDownloadService();
            }

            // الحصول على المحتوى من الخدمة الحقيقية
            string content = _realService.GetBookContent(isbn);

            // حفظ في الكاش
            _cache[isbn] = content;
            Console.WriteLine($"💾 تم حفظ محتوى الكتاب {isbn} في الكاش");

            return content;
        }

        private bool CheckDownloadPermission(string userEmail)
        {
            if (!_userDownloads.ContainsKey(userEmail))
            {
                return true;
            }

            return _userDownloads[userEmail].Count < MAX_DOWNLOADS_PER_USER;
        }

        private void RecordDownload(string userEmail, string isbn)
        {
            if (!_userDownloads.ContainsKey(userEmail))
            {
                _userDownloads[userEmail] = new List<string>();
            }

            _userDownloads[userEmail].Add(isbn);
            Console.WriteLine($"📊 تم تسجيل تحميل الكتاب {isbn} للمستخدم {userEmail}");
        }

        public void ShowUserStats(string userEmail)
        {
            if (_userDownloads.ContainsKey(userEmail))
            {
                var downloads = _userDownloads[userEmail];
                Console.WriteLine($"📈 إحصائيات المستخدم {userEmail}:");
                Console.WriteLine($"   عدد التحميلات: {downloads.Count}/{MAX_DOWNLOADS_PER_USER}");
                Console.WriteLine($"   الكتب المحملة: {string.Join(", ", downloads)}");
            }
            else
            {
                Console.WriteLine($"📈 المستخدم {userEmail} لم يحمل أي كتب بعد");
            }
        }
    }

    // ===== مثال شامل على الاستخدام =====
    public class StructuralPatternsDemo
    {
        public static void DemonstrateStructuralPatterns()
        {
            Console.WriteLine("=== Structural Patterns Demo ===\n");

            // 1. Facade Pattern Demo
            DemonstrateFacadePattern();

            // 2. Adapter Pattern Demo
            DemonstrateAdapterPattern();

            // 3. Decorator Pattern Demo
            DemonstrateDecoratorPattern();

            // 4. Proxy Pattern Demo
            DemonstrateProxyPattern();
        }

        private static void DemonstrateFacadePattern()
        {
            Console.WriteLine("1. FACADE PATTERN - نمط الواجهة");
            Console.WriteLine("=====================================");

            var library = new LibraryFacade();

            // تسجيل دخول وبحث وحجز
            library.LoginUser("admin", "password");
            library.SearchAndReserveBook("البرمجة", "<EMAIL>", "+966501234567");
            library.LogoutUser();

            Console.WriteLine("\n" + new string('=', 50) + "\n");
        }

        private static void DemonstrateAdapterPattern()
        {
            Console.WriteLine("2. ADAPTER PATTERN - نمط المحول");
            Console.WriteLine("=====================================");

            // النظام القديم
            var oldSystem = new OldBookSystem();

            // المحول
            var adapter = new BookSystemAdapter(oldSystem);

            // استخدام النظام القديم عبر الواجهة الجديدة
            string isbn = "978-3-16-148410-0";

            var bookInfo = adapter.GetBookDetails(isbn);
            Console.WriteLine($"📚 معلومات الكتاب: {bookInfo.Title}");
            Console.WriteLine($"📖 ISBN: {bookInfo.ISBN}");
            Console.WriteLine($"✅ متوفر: {bookInfo.IsAvailable}");

            if (adapter.IsBookAvailable(isbn))
            {
                adapter.LendBook(isbn, "<EMAIL>");
            }

            Console.WriteLine("\n" + new string('=', 50) + "\n");
        }

        private static void DemonstrateDecoratorPattern()
        {
            Console.WriteLine("3. DECORATOR PATTERN - نمط المزخرف");
            Console.WriteLine("=====================================");

            // كتاب أساسي
            IBook book = new BasicBook("تعلم البرمجة", "أحمد محمد", 50.00m);
            Console.WriteLine("الكتاب الأساسي:");
            book.Display();

            // إضافة غلاف صلب
            book = new HardcoverDecorator(book);
            Console.WriteLine("\nبعد إضافة الغلاف الصلب:");
            book.Display();

            // إضافة تغليف هدية
            book = new GiftWrapDecorator(book);
            Console.WriteLine("\nبعد إضافة تغليف الهدية:");
            book.Display();

            // إضافة شحن سريع
            book = new ExpressShippingDecorator(book);
            Console.WriteLine("\nبعد إضافة الشحن السريع:");
            book.Display();

            // مثال آخر: نسخة رقمية
            IBook digitalBook = new BasicBook("كتاب رقمي", "سارة أحمد", 30.00m);
            digitalBook = new DigitalVersionDecorator(digitalBook);
            Console.WriteLine("\nالكتاب الرقمي:");
            digitalBook.Display();

            Console.WriteLine("\n" + new string('=', 50) + "\n");
        }

        private static void DemonstrateProxyPattern()
        {
            Console.WriteLine("4. PROXY PATTERN - نمط الوكيل");
            Console.WriteLine("=====================================");

            var downloadProxy = new BookDownloadProxy();
            string user1 = "<EMAIL>";
            string user2 = "<EMAIL>";

            // تحميل كتب للمستخدم الأول
            Console.WriteLine("--- تحميلات المستخدم الأول ---");
            downloadProxy.DownloadBook("ISBN-001", user1);
            downloadProxy.DownloadBook("ISBN-002", user1);
            downloadProxy.DownloadBook("ISBN-003", user1);
            downloadProxy.ShowUserStats(user1);

            // محاولة تحميل كتاب إضافي (سيفشل)
            Console.WriteLine("\n--- محاولة تجاوز الحد الأقصى ---");
            downloadProxy.DownloadBook("ISBN-004", user1);

            // قراءة محتوى الكتب (مع الكاش)
            Console.WriteLine("\n--- قراءة المحتوى ---");
            var content1 = downloadProxy.GetBookContent("ISBN-001");
            Console.WriteLine($"المحتوى: {content1[..50]}...");

            // قراءة نفس الكتاب مرة أخرى (من الكاش)
            Console.WriteLine("\n--- قراءة نفس الكتاب مرة أخرى ---");
            var content2 = downloadProxy.GetBookContent("ISBN-001");

            // مستخدم جديد
            Console.WriteLine("\n--- مستخدم جديد ---");
            downloadProxy.DownloadBook("ISBN-005", user2);
            downloadProxy.ShowUserStats(user2);

            Console.WriteLine("\n" + new string('=', 50) + "\n");
        }
    }
}