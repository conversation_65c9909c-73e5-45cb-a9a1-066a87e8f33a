# 🎯 دليل شامل لأنماط التصميم (Design Patterns)

## 📋 فهرس المحتويات
- [مقدمة عن أنماط التصميم](#مقدمة)
- [التصنيف الهرمي للأنماط](#التصنيف-الهرمي)
- [الأنماط الإنشائية (Creational)](#الأنماط-الإنشائية)
- [الأنماط السلوكية (Behavioral)](#الأنماط-السلوكية)
- [الأنماط الهيكلية (Structural)](#الأنماط-الهيكلية)
- [أمثلة عملية](#أمثلة-عملية)
- [نصائح للاختبار](#نصائح-للاختبار)

---

## 🎯 مقدمة عن أنماط التصميم {#مقدمة}

### ما هي أنماط التصميم؟
أنماط التصميم هي **حلول مجربة ومختبرة** لمشاكل شائعة في تصميم البرمجيات. تم توثيقها لأول مرة في كتاب "Gang of Four" عام 1994.

### لماذا نستخدمها؟
- ✅ **إعادة الاستخدام**: حلول مجربة لمشاكل متكررة
- ✅ **التواصل**: لغة مشتركة بين المطورين
- ✅ **أفضل الممارسات**: تطبيق مبادئ البرمجة الجيدة
- ✅ **المرونة**: كود قابل للتوسع والصيانة

### المبادئ الأساسية
1. **البرمجة للواجهات وليس للتطبيق**
2. **تفضيل التركيب على الوراثة**
3. **فصل ما يتغير عما لا يتغير**

---

## 🏗️ التصنيف الهرمي للأنماط {#التصنيف-الهرمي}

```
Design Patterns (أنماط التصميم)
├── 🏭 Creational Patterns (الأنماط الإنشائية)
│   ├── Singleton
│   ├── Factory Method
│   ├── Abstract Factory
│   ├── Builder
│   └── 🎯 Prototype ← (مطلوب في الاختبار)
│
├── 🎭 Behavioral Patterns (الأنماط السلوكية)
│   ├── Observer
│   ├── Command
│   ├── 🎯 Strategy ← (مطلوب في الاختبار)
│   ├── 🎯 State ← (مطلوب في الاختبار)
│   ├── Template Method
│   ├── Chain of Responsibility
│   ├── Mediator
│   ├── Memento
│   ├── Visitor
│   └── Iterator
│
└── 🏗️ Structural Patterns (الأنماط الهيكلية)
    ├── 🎯 Adapter ← (مطلوب في الاختبار)
    ├── Bridge
    ├── Composite
    ├── 🎯 Decorator ← (مطلوب في الاختبار)
    ├── 🎯 Facade ← (مطلوب في الاختبار)
    ├── Flyweight
    └── 🎯 Proxy ← (مطلوب في الاختبار)
```

---

## 🏭 الأنماط الإنشائية (Creational Patterns) {#الأنماط-الإنشائية}

### 📖 التعريف
تتعامل مع **آليات إنشاء الكائنات** بطريقة تزيد من مرونة وإعادة استخدام الكود.

### 🎯 Prototype Pattern

#### 💡 المفهوم
إنشاء كائنات جديدة عن طريق **استنساخ** كائن موجود بدلاً من إنشائه من الصفر.

#### 🔧 متى نستخدمه؟
- عندما يكون إنشاء الكائن **مكلفاً** (expensive)
- عندما نريد تجنب **التعقيد** في إنشاء كائنات متشابهة
- عندما نريد إنشاء كائنات بناءً على **نموذج أولي**

#### 🏗️ الهيكل
```
IPrototype
├── Clone() : IPrototype
│
ConcretePrototype
├── Clone() : ConcretePrototype
├── Data
└── Methods

PrototypeManager
├── prototypes: Dictionary
├── RegisterPrototype()
├── ClonePrototype()
└── GetPrototype()
```

#### ✅ المزايا
- تحسين الأداء
- تجنب التعقيد في الإنشاء
- مرونة في إنشاء أنواع مختلفة

#### ❌ العيوب
- تعقيد في الاستنساخ العميق
- صعوبة في استنساخ الكائنات المعقدة

#### 🎯 مثال عملي
```csharp
// نظام إدارة الوثائق
var reportTemplate = new ReportDocument("تقرير أساسي", "محتوى...");
var januaryReport = reportTemplate.Clone();
januaryReport.SetTitle("تقرير يناير");
```

---

## 🎭 الأنماط السلوكية (Behavioral Patterns) {#الأنماط-السلوكية}

### 📖 التعريف
تركز على **التواصل بين الكائنات** وتوزيع المسؤوليات بينها.

### 🎯 Strategy Pattern

#### 💡 المفهوم
تعريف عائلة من **الخوارزميات** وجعلها قابلة للتبديل في وقت التشغيل.

#### 🔧 متى نستخدمه؟
- عندما يكون لدينا **خوارزميات متعددة** لنفس المهمة
- عندما نريد تجنب **if/else** أو **switch** المعقدة
- عندما نريد إضافة خوارزميات جديدة بسهولة

#### 🏗️ الهيكل
```
IStrategy
├── Execute()
│
ConcreteStrategyA
├── Execute()
│
ConcreteStrategyB
├── Execute()
│
Context
├── strategy: IStrategy
├── SetStrategy()
└── ExecuteStrategy()
```

#### ✅ المزايا
- مرونة في تغيير الخوارزميات
- سهولة إضافة استراتيجيات جديدة
- فصل الخوارزميات عن الكود المستخدم

#### ❌ العيوب
- زيادة عدد الكلاسات
- العميل يجب أن يعرف الاستراتيجيات المختلفة

#### 🎯 مثال عملي
```csharp
// نظام الدفع
var paymentProcessor = new PaymentProcessor(new CreditCardStrategy());
paymentProcessor.ProcessPayment(100.50m, details);

// تغيير الاستراتيجية
paymentProcessor.SetStrategy(new PayPalStrategy());
paymentProcessor.ProcessPayment(75.25m, details);
```

### 🎯 State Pattern

#### 💡 المفهوم
السماح للكائن بتغيير **سلوكه** عندما تتغير حالته الداخلية.

#### 🔧 متى نستخدمه؟
- عندما يكون سلوك الكائن يعتمد على **حالته**
- عندما نريد تجنب **if/else** المعقدة للحالات
- عندما تكون الحالات كثيرة ومعقدة

#### 🏗️ الهيكل
```
IState
├── Handle()
│
ConcreteStateA
├── Handle()
│
ConcreteStateB
├── Handle()
│
Context
├── currentState: IState
├── SetState()
└── Request()
```

#### ✅ المزايا
- تنظيم الكود حسب الحالات
- سهولة إضافة حالات جديدة
- وضوح في انتقالات الحالة

#### ❌ العيوب
- زيادة عدد الكلاسات
- تعقيد في الحالات البسيطة

#### 🎯 مثال عملي
```csharp
// آلة بيع المشروبات
var vendingMachine = new VendingMachine();
vendingMachine.InsertCoin(2.50m);  // الحالة: HasMoney
vendingMachine.SelectProduct("A1"); // الحالة: ProductSelected
vendingMachine.DispenseProduct();   // الحالة: Dispensing → Idle
```

---

## 🏗️ الأنماط الهيكلية (Structural Patterns) {#الأنماط-الهيكلية}

### 📖 التعريف
تتعامل مع **تركيب الكائنات والكلاسات** لتكوين هياكل أكبر مع الحفاظ على المرونة والكفاءة.

### 🎯 Facade Pattern

#### 💡 المفهوم
توفير **واجهة مبسطة** لنظام معقد من الكلاسات والواجهات.

#### 🔧 متى نستخدمه؟
- عندما يكون النظام **معقد** ويحتوي على كلاسات كثيرة
- عندما نريد **تبسيط** التفاعل مع النظام
- عندما نريد **فصل** العميل عن تعقيدات النظام

#### 🏗️ الهيكل
```
Facade
├── simpleMethod1()
├── simpleMethod2()
└── complexOperation()
    ├── subsystem1.operation()
    ├── subsystem2.operation()
    └── subsystem3.operation()

SubSystem1, SubSystem2, SubSystem3
├── complexOperation1()
├── complexOperation2()
└── complexOperation3()
```

#### ✅ المزايا
- تبسيط الواجهة
- فصل العميل عن النظام المعقد
- سهولة الاستخدام

#### ❌ العيوب
- قد يصبح الـ Facade معقد جداً
- قد يخفي وظائف مهمة

#### 🎯 مثال عملي
```csharp
// نظام المكتبة
var library = new LibraryFacade();
library.LoginUser("admin", "password");
library.SearchAndReserveBook("البرمجة", "<EMAIL>", "+966501234567");
library.LogoutUser();
```

### 🎯 Adapter Pattern

#### 💡 المفهوم
تحويل **واجهة كلاس** إلى واجهة أخرى متوقعة من العميل.

#### 🔧 متى نستخدمه؟
- عندما نريد استخدام **كلاس موجود** بواجهة مختلفة
- عندما نريد دمج **أنظمة قديمة** مع جديدة
- عندما تكون الواجهات **غير متوافقة**

#### 🏗️ الهيكل
```
Target (الواجهة المطلوبة)
├── Request()

Adapter
├── Request()
└── adaptee: Adaptee

Adaptee (النظام القديم)
├── SpecificRequest()
```

#### ✅ المزايا
- إعادة استخدام الكود الموجود
- فصل الواجهة عن التطبيق
- دمج الأنظمة المختلفة

#### ❌ العيوب
- زيادة تعقيد الكود
- طبقة إضافية من التجريد

#### 🎯 مثال عملي
```csharp
// تحويل نظام الكتب القديم للواجهة الجديدة
var oldSystem = new OldBookSystem();
var adapter = new BookSystemAdapter(oldSystem);
var bookInfo = adapter.GetBookDetails("978-3-16-148410-0");
```

### 🎯 Decorator Pattern

#### 💡 المفهوم
إضافة **سلوكيات جديدة** للكائنات دون تعديل هيكلها الأساسي.

#### 🔧 متى نستخدمه؟
- عندما نريد إضافة **وظائف جديدة** للكائنات
- عندما نريد تجنب **الوراثة المعقدة**
- عندما نريد **مرونة** في إضافة/إزالة الوظائف

#### 🏗️ الهيكل
```
Component
├── Operation()

ConcreteComponent
├── Operation()

Decorator
├── component: Component
├── Operation()

ConcreteDecorator
├── Operation()
├── AddedBehavior()
```

#### ✅ المزايا
- مرونة في إضافة الوظائف
- تجنب الوراثة المعقدة
- إمكانية التركيب المتعدد

#### ❌ العيوب
- زيادة عدد الكائنات الصغيرة
- تعقيد في التتبع

#### 🎯 مثال عملي
```csharp
// تزيين الكتاب بخصائص إضافية
IBook book = new BasicBook("تعلم البرمجة", "أحمد محمد", 50.00m);
book = new HardcoverDecorator(book);      // غلاف صلب
book = new GiftWrapDecorator(book);       // تغليف هدية
book = new ExpressShippingDecorator(book); // شحن سريع
```

### 🎯 Proxy Pattern

#### 💡 المفهوم
توفير **بديل أو وكيل** لكائن آخر للتحكم في الوصول إليه.

#### 🔧 متى نستخدمه؟
- عندما نريد **التحكم في الوصول** للكائن
- عندما نريد **تأخير إنشاء** الكائن (Lazy Loading)
- عندما نريد إضافة **وظائف إضافية** (Caching, Logging)

#### 🏗️ الهيكل
```
Subject
├── Request()

RealSubject
├── Request()

Proxy
├── realSubject: RealSubject
├── Request()
├── CheckAccess()
└── LogAccess()
```

#### ✅ المزايا
- التحكم في الوصول
- تحسين الأداء (Caching)
- الإنشاء المتأخر

#### ❌ العيوب
- زيادة تعقيد الكود
- تأخير في الاستجابة أحياناً

#### 🎯 مثال عملي
```csharp
// وكيل لخدمة تحميل الكتب
var downloadProxy = new BookDownloadProxy();
downloadProxy.DownloadBook("ISBN-001", "<EMAIL>"); // فحص الصلاحيات
var content = downloadProxy.GetBookContent("ISBN-001");      // من الكاش
```

---

## 💻 أمثلة عملية {#أمثلة-عملية}

### 🏃‍♂️ تشغيل الأمثلة

```bash
# تشغيل جميع الأمثلة
dotnet run --project design_patterns/DesignPatterns.csproj

# أو تشغيل مثال محدد
echo "1" | dotnet run  # Prototype Pattern
echo "2" | dotnet run  # Strategy Pattern
echo "3" | dotnet run  # State Pattern
echo "4" | dotnet run  # Structural Patterns
```

### 📁 هيكل المشروع

```
design_patterns/
├── PrototypePattern.cs      # نمط النموذج الأولي
├── StrategyPattern.cs       # نمط الاستراتيجية
├── StatePattern.cs          # نمط الحالة
├── StructuralPatterns.cs    # الأنماط الهيكلية
├── Program.cs               # البرنامج الرئيسي
└── DesignPatterns.csproj    # ملف المشروع
```

### 🔗 الربط بين الأنماط

```csharp
// مثال: دمج عدة أنماط في نظام واحد
public class LibrarySystem
{
    // Facade: واجهة مبسطة
    private LibraryFacade _facade;

    // Strategy: استراتيجيات البحث
    private ISearchStrategy _searchStrategy;

    // State: حالة النظام
    private ISystemState _currentState;

    // Prototype: نماذج أولية للوثائق
    private DocumentPrototypeManager _prototypes;

    // Decorator: تزيين الكتب
    private IBookDecorator _bookDecorator;

    // Proxy: وكيل للتحميل
    private IBookDownloadService _downloadProxy;
}
```

---

## 🎯 نصائح للاختبار {#نصائح-للاختبار}

### 📝 أسئلة متوقعة

#### 1. أسئلة التعريف
- **ما هو نمط Prototype؟**
  - إنشاء كائنات جديدة عن طريق استنساخ كائن موجود

- **متى نستخدم Strategy Pattern؟**
  - عندما يكون لدينا خوارزميات متعددة لنفس المهمة

- **ما الفرق بين Strategy و State؟**
  - Strategy: تغيير الخوارزمية
  - State: تغيير السلوك حسب الحالة

#### 2. أسئلة التطبيق
- **اكتب كود لـ Facade Pattern**
- **صمم نظام باستخدام Decorator Pattern**
- **اشرح كيف يعمل Proxy Pattern مع مثال**

#### 3. أسئلة المقارنة
- **قارن بين Adapter و Facade**
  - Adapter: تحويل واجهة موجودة
  - Facade: تبسيط نظام معقد

- **قارن بين Decorator و Inheritance**
  - Decorator: مرونة في وقت التشغيل
  - Inheritance: ثابت في وقت التصميم

### 🧠 استراتيجيات الحفظ

#### 1. الربط بالأمثلة الواقعية
- **Prototype**: نسخ ولصق في المحرر
- **Strategy**: طرق الدفع المختلفة
- **State**: آلة البيع أو إشارة المرور
- **Facade**: واجهة التطبيق المبسطة
- **Adapter**: محول الكهرباء
- **Decorator**: إضافات القهوة
- **Proxy**: الوكيل العقاري

#### 2. الكلمات المفتاحية
- **Prototype**: Clone, Copy, Template
- **Strategy**: Algorithm, Interchangeable
- **State**: Behavior, Transition
- **Facade**: Simplify, Unified Interface
- **Adapter**: Convert, Legacy
- **Decorator**: Add, Enhance
- **Proxy**: Control, Lazy Loading

#### 3. الرسوم البيانية
```
Prototype: Original → Clone1, Clone2, Clone3
Strategy: Context → [Strategy1|Strategy2|Strategy3]
State: Context → State1 → State2 → State3
Facade: Client → Facade → [SubSystem1, SubSystem2, SubSystem3]
Adapter: Client → Adapter → Adaptee
Decorator: Component → Decorator1 → Decorator2
Proxy: Client → Proxy → RealSubject
```

### ✅ قائمة مراجعة قبل الاختبار

- [ ] فهم **تعريف** كل نمط
- [ ] معرفة **متى نستخدم** كل نمط
- [ ] حفظ **الهيكل الأساسي** لكل نمط
- [ ] فهم **المزايا والعيوب**
- [ ] القدرة على **كتابة كود** بسيط
- [ ] فهم **الفروقات** بين الأنماط المتشابهة
- [ ] ربط الأنماط **بأمثلة واقعية**

### 🎯 نصائح أثناء الاختبار

1. **اقرأ السؤال بعناية** - حدد النمط المطلوب
2. **ابدأ بالهيكل الأساسي** - الواجهات والكلاسات الرئيسية
3. **اكتب كود بسيط وواضح** - لا تعقد الأمور
4. **استخدم أسماء واضحة** - تساعد في فهم النمط
5. **اشرح منطق الحل** - لماذا اخترت هذا النمط؟

---

## 🚀 الخطوات التالية

1. **مراجعة الأمثلة العملية** في المجلد `design_patterns/`
2. **تشغيل الكود** وفهم كيفية عمل كل نمط
3. **تطبيق الأنماط** في مشاريعك الشخصية
4. **حل تمارين إضافية** لتعميق الفهم
5. **مراجعة الأنماط الأخرى** (Observer, Factory, Singleton, etc.)

---

## 📚 مصادر إضافية

- [Gang of Four Design Patterns Book](https://en.wikipedia.org/wiki/Design_Patterns)
- [Refactoring Guru - Design Patterns](https://refactoring.guru/design-patterns)
- [Microsoft Docs - Design Patterns](https://docs.microsoft.com/en-us/azure/architecture/patterns/)

---

**بالتوفيق في الاختبار! 🎯**