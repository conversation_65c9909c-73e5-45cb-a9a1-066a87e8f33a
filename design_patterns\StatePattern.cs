/*
State Pattern - نمط الحالة
==========================

المفهوم: السماح للكائن بتغيير سلوكه عندما تتغير حالته الداخلية
الهدف: تجنب استخدام if/else أو switch كثيرة لإدارة الحالات المختلفة

مثال عملي: آلة بيع المشروبات
*/

using System;
using System.Collections.Generic;

namespace DesignPatterns.State
{
    // 1. واجهة الحالة الأساسية
    public interface IVendingMachineState
    {
        /// <summary>
        /// إدراج عملة معدنية
        /// </summary>
        void InsertCoin(VendingMachine machine, decimal amount);

        /// <summary>
        /// اختيار منتج
        /// </summary>
        void SelectProduct(VendingMachine machine, string productCode);

        /// <summary>
        /// إلغاء العملية واسترداد النقود
        /// </summary>
        void Cancel(VendingMachine machine);

        /// <summary>
        /// صرف المنتج
        /// </summary>
        void DispenseProduct(VendingMachine machine);

        /// <summary>
        /// الحصول على اسم الحالة
        /// </summary>
        string GetStateName();
    }

    // 2. كلاس المنتج
    public class Product
    {
        public string Code { get; set; } = "";
        public string Name { get; set; } = "";
        public decimal Price { get; set; }
        public int Quantity { get; set; }

        public Product(string code, string name, decimal price, int quantity)
        {
            Code = code;
            Name = name;
            Price = price;
            Quantity = quantity;
        }

        public override string ToString()
        {
            return $"{Name} ({Code}) - {Price:C} - متوفر: {Quantity}";
        }
    }

    // 3. آلة البيع الرئيسية (Context)
    public class VendingMachine
    {
        private IVendingMachineState _currentState;
        private Dictionary<string, Product> _products;
        private decimal _insertedAmount;
        private string _selectedProductCode = "";

        // الحالات المختلفة
        public IVendingMachineState IdleState { get; private set; }
        public IVendingMachineState HasMoneyState { get; private set; }
        public IVendingMachineState ProductSelectedState { get; private set; }
        public IVendingMachineState DispensingState { get; private set; }
        public IVendingMachineState OutOfStockState { get; private set; }

        public VendingMachine()
        {
            // إنشاء الحالات
            IdleState = new IdleState();
            HasMoneyState = new HasMoneyState();
            ProductSelectedState = new ProductSelectedState();
            DispensingState = new DispensingState();
            OutOfStockState = new OutOfStockState();

            // تعيين الحالة الأولية
            _currentState = IdleState;
            _insertedAmount = 0;

            // إضافة منتجات تجريبية
            _products = new Dictionary<string, Product>
            {
                { "A1", new Product("A1", "كوكا كولا", 2.50m, 10) },
                { "A2", new Product("A2", "بيبسي", 2.50m, 8) },
                { "B1", new Product("B1", "ماء", 1.00m, 15) },
                { "B2", new Product("B2", "عصير برتقال", 3.00m, 5) },
                { "C1", new Product("C1", "شيبس", 1.50m, 0) }, // نفد المخزون
                { "C2", new Product("C2", "شوكولاتة", 2.00m, 12) }
            };
        }

        // Properties للوصول للبيانات
        public decimal InsertedAmount => _insertedAmount;
        public string SelectedProductCode => _selectedProductCode;
        public Dictionary<string, Product> Products => _products;

        // تغيير الحالة
        public void SetState(IVendingMachineState state)
        {
            Console.WriteLine($"تغيير الحالة من {_currentState.GetStateName()} إلى {state.GetStateName()}");
            _currentState = state;
        }

        // إضافة مبلغ
        public void AddAmount(decimal amount)
        {
            _insertedAmount += amount;
        }

        // تعيين المنتج المختار
        public void SetSelectedProduct(string productCode)
        {
            _selectedProductCode = productCode;
        }

        // إعادة تعيين البيانات
        public void Reset()
        {
            _insertedAmount = 0;
            _selectedProductCode = "";
        }

        // تقليل كمية المنتج
        public void ReduceProductQuantity(string productCode)
        {
            if (_products.ContainsKey(productCode))
            {
                _products[productCode].Quantity--;
            }
        }

        // العمليات الرئيسية
        public void InsertCoin(decimal amount)
        {
            _currentState.InsertCoin(this, amount);
        }

        public void SelectProduct(string productCode)
        {
            _currentState.SelectProduct(this, productCode);
        }

        public void Cancel()
        {
            _currentState.Cancel(this);
        }

        public void DispenseProduct()
        {
            _currentState.DispenseProduct(this);
        }

        public void DisplayStatus()
        {
            Console.WriteLine($"\n--- حالة الآلة ---");
            Console.WriteLine($"الحالة الحالية: {_currentState.GetStateName()}");
            Console.WriteLine($"المبلغ المدرج: {_insertedAmount:C}");
            Console.WriteLine($"المنتج المختار: {_selectedProductCode}");
            Console.WriteLine("المنتجات المتاحة:");
            foreach (var product in _products.Values)
            {
                Console.WriteLine($"  {product}");
            }
            Console.WriteLine();
        }
    }

    // 4. تطبيقات الحالات المختلفة

    // حالة الخمول - لا يوجد نقود مدرجة
    public class IdleState : IVendingMachineState
    {
        public string GetStateName() => "خامل";

        public void InsertCoin(VendingMachine machine, decimal amount)
        {
            Console.WriteLine($"تم إدراج {amount:C}");
            machine.AddAmount(amount);
            machine.SetState(machine.HasMoneyState);
        }

        public void SelectProduct(VendingMachine machine, string productCode)
        {
            Console.WriteLine("يرجى إدراج النقود أولاً");
        }

        public void Cancel(VendingMachine machine)
        {
            Console.WriteLine("لا يوجد شيء لإلغائه");
        }

        public void DispenseProduct(VendingMachine machine)
        {
            Console.WriteLine("لا يمكن صرف منتج بدون نقود");
        }
    }

    // حالة وجود نقود
    public class HasMoneyState : IVendingMachineState
    {
        public string GetStateName() => "يحتوي على نقود";

        public void InsertCoin(VendingMachine machine, decimal amount)
        {
            Console.WriteLine($"تم إدراج {amount:C} إضافي");
            machine.AddAmount(amount);
            Console.WriteLine($"إجمالي المبلغ: {machine.InsertedAmount:C}");
        }

        public void SelectProduct(VendingMachine machine, string productCode)
        {
            if (!machine.Products.ContainsKey(productCode))
            {
                Console.WriteLine("رمز المنتج غير صحيح");
                return;
            }

            var product = machine.Products[productCode];

            if (product.Quantity <= 0)
            {
                Console.WriteLine($"المنتج {product.Name} غير متوفر");
                machine.SetState(machine.OutOfStockState);
                return;
            }

            if (machine.InsertedAmount < product.Price)
            {
                Console.WriteLine($"المبلغ غير كافي. السعر: {product.Price:C}, المبلغ المدرج: {machine.InsertedAmount:C}");
                return;
            }

            Console.WriteLine($"تم اختيار {product.Name}");
            machine.SetSelectedProduct(productCode);
            machine.SetState(machine.ProductSelectedState);
        }

        public void Cancel(VendingMachine machine)
        {
            Console.WriteLine($"تم إرجاع {machine.InsertedAmount:C}");
            machine.Reset();
            machine.SetState(machine.IdleState);
        }

        public void DispenseProduct(VendingMachine machine)
        {
            Console.WriteLine("يرجى اختيار منتج أولاً");
        }
    }

    // حالة اختيار المنتج
    public class ProductSelectedState : IVendingMachineState
    {
        public string GetStateName() => "تم اختيار المنتج";

        public void InsertCoin(VendingMachine machine, decimal amount)
        {
            Console.WriteLine($"تم إدراج {amount:C} إضافي");
            machine.AddAmount(amount);
            Console.WriteLine($"إجمالي المبلغ: {machine.InsertedAmount:C}");
        }

        public void SelectProduct(VendingMachine machine, string productCode)
        {
            Console.WriteLine("تم اختيار منتج بالفعل. يرجى المتابعة للصرف أو الإلغاء");
        }

        public void Cancel(VendingMachine machine)
        {
            Console.WriteLine($"تم إلغاء العملية وإرجاع {machine.InsertedAmount:C}");
            machine.Reset();
            machine.SetState(machine.IdleState);
        }

        public void DispenseProduct(VendingMachine machine)
        {
            var product = machine.Products[machine.SelectedProductCode];
            Console.WriteLine($"جاري صرف {product.Name}...");

            machine.SetState(machine.DispensingState);

            // محاكاة وقت الصرف
            System.Threading.Thread.Sleep(1000);

            // تقليل الكمية
            machine.ReduceProductQuantity(machine.SelectedProductCode);

            // حساب الباقي
            var change = machine.InsertedAmount - product.Price;
            if (change > 0)
            {
                Console.WriteLine($"تم صرف {product.Name} وإرجاع {change:C}");
            }
            else
            {
                Console.WriteLine($"تم صرف {product.Name}");
            }

            machine.Reset();
            machine.SetState(machine.IdleState);
        }
    }

    // حالة الصرف
    public class DispensingState : IVendingMachineState
    {
        public string GetStateName() => "جاري الصرف";

        public void InsertCoin(VendingMachine machine, decimal amount)
        {
            Console.WriteLine("يرجى الانتظار حتى انتهاء عملية الصرف");
        }

        public void SelectProduct(VendingMachine machine, string productCode)
        {
            Console.WriteLine("يرجى الانتظار حتى انتهاء عملية الصرف");
        }

        public void Cancel(VendingMachine machine)
        {
            Console.WriteLine("لا يمكن الإلغاء أثناء عملية الصرف");
        }

        public void DispenseProduct(VendingMachine machine)
        {
            Console.WriteLine("جاري الصرف بالفعل...");
        }
    }

    // حالة نفاد المخزون
    public class OutOfStockState : IVendingMachineState
    {
        public string GetStateName() => "نفد المخزون";

        public void InsertCoin(VendingMachine machine, decimal amount)
        {
            Console.WriteLine($"تم إدراج {amount:C}");
            machine.AddAmount(amount);
            Console.WriteLine("يرجى اختيار منتج آخر متوفر");
        }

        public void SelectProduct(VendingMachine machine, string productCode)
        {
            if (!machine.Products.ContainsKey(productCode))
            {
                Console.WriteLine("رمز المنتج غير صحيح");
                return;
            }

            var product = machine.Products[productCode];

            if (product.Quantity <= 0)
            {
                Console.WriteLine($"المنتج {product.Name} غير متوفر أيضاً");
                return;
            }

            if (machine.InsertedAmount < product.Price)
            {
                Console.WriteLine($"المبلغ غير كافي. السعر: {product.Price:C}, المبلغ المدرج: {machine.InsertedAmount:C}");
                machine.SetState(machine.HasMoneyState);
                return;
            }

            Console.WriteLine($"تم اختيار {product.Name}");
            machine.SetSelectedProduct(productCode);
            machine.SetState(machine.ProductSelectedState);
        }

        public void Cancel(VendingMachine machine)
        {
            Console.WriteLine($"تم إرجاع {machine.InsertedAmount:C}");
            machine.Reset();
            machine.SetState(machine.IdleState);
        }

        public void DispenseProduct(VendingMachine machine)
        {
            Console.WriteLine("يرجى اختيار منتج متوفر أولاً");
        }
    }

    // 5. مثال على الاستخدام
    public class StatePatternDemo
    {
        public static void DemonstrateStatePattern()
        {
            Console.WriteLine("=== State Pattern Demo ===\n");

            var vendingMachine = new VendingMachine();
            vendingMachine.DisplayStatus();

            // سيناريو 1: شراء ناجح
            Console.WriteLine("=== سيناريو 1: شراء ناجح ===");
            vendingMachine.InsertCoin(3.00m);
            vendingMachine.SelectProduct("A1"); // كوكا كولا 2.50
            vendingMachine.DispenseProduct();
            vendingMachine.DisplayStatus();

            // سيناريو 2: مبلغ غير كافي
            Console.WriteLine("\n=== سيناريو 2: مبلغ غير كافي ===");
            vendingMachine.InsertCoin(1.00m);
            vendingMachine.SelectProduct("B2"); // عصير برتقال 3.00
            vendingMachine.InsertCoin(2.50m); // إضافة مبلغ إضافي
            vendingMachine.SelectProduct("B2");
            vendingMachine.DispenseProduct();

            // سيناريو 3: منتج غير متوفر
            Console.WriteLine("\n=== سيناريو 3: منتج غير متوفر ===");
            vendingMachine.InsertCoin(2.00m);
            vendingMachine.SelectProduct("C1"); // شيبس - نفد المخزون
            vendingMachine.SelectProduct("C2"); // شوكولاتة - متوفر
            vendingMachine.DispenseProduct();

            // سيناريو 4: إلغاء العملية
            Console.WriteLine("\n=== سيناريو 4: إلغاء العملية ===");
            vendingMachine.InsertCoin(5.00m);
            vendingMachine.SelectProduct("A2");
            vendingMachine.Cancel();

            vendingMachine.DisplayStatus();
        }
    }
}